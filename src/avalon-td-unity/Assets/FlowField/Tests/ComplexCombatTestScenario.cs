using System.Collections;
using System.Collections.Generic;
using Avalon.NPC.Enemies;
using Avalon.Simulation.Combat;
using Avalon.Simulation.Combat.Core;
using Avalon.Simulation.Movement;
using UnityEngine;
using Unity.Entities;
using Unity.Mathematics;
using Unity.Deterministic.Mathematics;
using Unity.Transforms;
using Unity.Collections;
using FlowField;

namespace FlowField.Tests
{
    /// <summary>
    /// Complex combat test scenario for FlowField system
    /// Extends the movement test scenario with combat functionality
    /// Tests different attack patterns, targeting strategies, and combat visualization
    /// </summary>
    public class ComplexCombatTestScenario : MonoBehaviour
    {
        [Header("Grid Configuration")]
        [SerializeField] private int2 gridSize = new int2(250, 250);
        [SerializeField] private dfloat2 cellSize = new dfloat2(dfloat.One, dfloat.One);
        [SerializeField] private dfloat2 worldOrigin = dfloat2.zero;

        [Header("Unit Spawning")]
        [SerializeField] private List<EnemyDefinition> combatUnitTypes = new List<EnemyDefinition>();
        [SerializeField] private float spawnInterval = 1.5f;
        [SerializeField] private int maxUnitsPerSpawner = 30;
        [SerializeField] private int spawnerCount = 6;

        [Header("Combat Configuration")]
        [SerializeField] private int towerCount = 12;
        [SerializeField] private float towerSpacing = 25.0f;
        [SerializeField] private bool enableFriendlyFire = false;
        [SerializeField] private bool enableAdvancedCombat = true;

        [Header("Attack Patterns")]
        [SerializeField] private bool testSingleTarget = true;
        [SerializeField] private bool testAOEAttacks = true;
        [SerializeField] private bool testLinearAttacks = true;
        [SerializeField] private bool testChainAttacks = true;
        [SerializeField] private bool testDOTEffects = true;

        [Header("Visualization")]
        [SerializeField] private bool enableCombatVisualization = true;
        [SerializeField] private bool enableGridVisualization = true;
        [SerializeField] private bool enableUnitVisualization = true;
        [SerializeField] private bool enableTargetVisualization = true;
        [SerializeField] private bool enableProjectileVisualization = true;
        [SerializeField] private Material friendlyUnitMaterial;
        [SerializeField] private Material enemyUnitMaterial;
        [SerializeField] private Material towerMaterial;
        [SerializeField] private Material projectileMaterial;
        [SerializeField] private Material explosionMaterial;

        // Visualization components
        private FlowField.Visualization.GridVisualization gridVisualization;
        private FlowField.Visualization.UnitVisualization unitVisualization;
        private FlowField.Visualization.TargetVisualization targetVisualization;
        private FlowField.Visualization.ProjectileVisualization projectileVisualization;

        // ECS Components
        private World world;
        private EntityManager entityManager;
        private List<Entity> flowFieldEntities = new List<Entity>();
        private List<Entity> targetEntities = new List<Entity>();
        private List<Entity> towerEntities = new List<Entity>();
        private List<Entity> friendlyUnits = new List<Entity>();
        private List<Entity> enemyUnits = new List<Entity>();
        private List<Vector3> spawnerPositions = new List<Vector3>();

        // Combat state
        private float lastSpawnTime;
        private int currentSpawnerIndex = 0;
        private Dictionary<int, int> spawnerUnitCounts = new Dictionary<int, int>();
        private int totalKills = 0;
        private int totalDamageDealt = 0;

        // Team configuration
        private const int FRIENDLY_TEAM = 1;
        private const int ENEMY_TEAM = 2;

        void Start()
        {
            InitializeECS();
            CreateLargeGrid();
            CreateObstacles();
            CreateTargets();
            CreateTowers();
            SetupSpawners();
            StartCoroutine(PeriodicUnitSpawning());
            StartCoroutine(CombatStatistics());

            // Initialize new visualization system
            if (enableCombatVisualization)
            {
                InitializeVisualizationComponents();
            }

            Debug.Log($"Complex Combat Test Scenario initialized");
            Debug.Log($"Grid: {gridSize.x}x{gridSize.y}, Towers: {towerCount}, Spawners: {spawnerCount}");
            Debug.Log($"Attack Patterns: Single={testSingleTarget}, AOE={testAOEAttacks}, Linear={testLinearAttacks}, Chain={testChainAttacks}, DOT={testDOTEffects}");
        }

        private void InitializeECS()
        {
            world = World.DefaultGameObjectInjectionWorld;
            entityManager = world.EntityManager;
        }

        private void CreateLargeGrid()
        {
            // Create flow field grids for different movement types
            var movementTypes = new MovementType[] 
            { 
                MovementType.Ground, 
                MovementType.Amphibious, 
                MovementType.Flying 
            };

            for (int i = 0; i < movementTypes.Length; i++)
            {
                var flowFieldEntity = entityManager.CreateEntity();
                
                var flowFieldGrid = FlowFieldUtils.CreateFlowFieldGrid(
                    gridSize, 
                    cellSize, 
                    worldOrigin,
                    Entity.Null,
                    i + 1,
                    movementTypes[i]
                );
                
                entityManager.AddComponentData(flowFieldEntity, flowFieldGrid);
                
                var buffer = entityManager.AddBuffer<FlowFieldCellBuffer>(flowFieldEntity);
                InitializeFlowFieldCells(buffer, gridSize, movementTypes[i]);
                
                flowFieldEntities.Add(flowFieldEntity);
            }
        }

        private void InitializeFlowFieldCells(DynamicBuffer<FlowFieldCellBuffer> buffer, int2 gridSize, MovementType movementType)
        {
            buffer.ResizeUninitialized(gridSize.x * gridSize.y);
            
            for (int y = 0; y < gridSize.y; y++)
            {
                for (int x = 0; x < gridSize.x; x++)
                {
                    var index = y * gridSize.x + x;
                    var cell = new FlowFieldCell();
                    
                    cell.SetWalkable(MovementType.Ground, true);
                    cell.SetWalkable(MovementType.Amphibious, true);
                    cell.SetWalkable(MovementType.Flying, true);
                    
                    cell.cost = (dfloat)1;
                    //cell.bestCost = ushort.MaxValue;
                    cell.direction = dfloat2.zero;
                    
                    buffer[index] = new FlowFieldCellBuffer { cell = cell };
                }
            }
        }

        private void CreateObstacles()
        {
            var random = new Unity.Mathematics.Random(12345);
            var obstacleGroupCount = 10; // Fewer obstacles for combat scenario
            var obstacleGroupSize = new int2(6, 6);
            
            for (int group = 0; group < obstacleGroupCount; group++)
            {
                var groupCenter = new int2(
                    random.NextInt(obstacleGroupSize.x, gridSize.x - obstacleGroupSize.x),
                    random.NextInt(obstacleGroupSize.y, gridSize.y - obstacleGroupSize.y)
                );

                CreateObstacleGroup(groupCenter, obstacleGroupSize, random);
            }
        }

        private void CreateObstacleGroup(int2 center, int2 size, Unity.Mathematics.Random random)
        {
            for (int y = 0; y < size.y; y++)
            {
                for (int x = 0; x < size.x; x++)
                {
                    var pos = center + new int2(x - size.x / 2, y - size.y / 2);
                    
                    // Create strategic obstacle patterns for combat
                    bool shouldCreateObstacle = false;
                    
                    var pattern = random.NextInt(0, 3);
                    switch (pattern)
                    {
                        case 0: // Defensive walls
                            shouldCreateObstacle = (x == 0 || x == size.x - 1);
                            break;
                        case 1: // Scattered cover
                            shouldCreateObstacle = random.NextFloat() < 0.4f;
                            break;
                        case 2: // Chokepoints
                            shouldCreateObstacle = (y == size.y / 2 && x != size.x / 2);
                            break;
                    }

                    if (shouldCreateObstacle && IsValidPosition(pos))
                    {
                        CreateObstacleAtPosition(pos);
                    }
                }
            }
        }

        private bool IsValidPosition(int2 pos)
        {
            return pos.x >= 0 && pos.x < gridSize.x && pos.y >= 0 && pos.y < gridSize.y;
        }

        private void CreateObstacleAtPosition(int2 gridPos)
        {
            foreach (var flowFieldEntity in flowFieldEntities)
            {
                var buffer = entityManager.GetBuffer<FlowFieldCellBuffer>(flowFieldEntity);
                
                FlowFieldUtils.SetObstacle(buffer, gridPos, gridSize, MovementType.Ground, false);
                FlowFieldUtils.SetObstacle(buffer, gridPos, gridSize, MovementType.Amphibious, false);
                
                // Flying units can pass over most obstacles
                if (UnityEngine.Random.value < 0.2f)
                {
                    FlowFieldUtils.SetObstacle(buffer, gridPos, gridSize, MovementType.Flying, false);
                }
            }

            if (enableCombatVisualization)
            {
                CreateObstacleVisual(gridPos);
            }
        }

        private void CreateObstacleVisual(int2 gridPos)
        {
            var worldPos = new Vector3(
                gridPos.x * (float)cellSize.x + (float)worldOrigin.x,
                0.5f,
                gridPos.y * (float)cellSize.y + (float)worldOrigin.y
            );

            var obstacle = GameObject.CreatePrimitive(PrimitiveType.Cube);
            obstacle.transform.position = worldPos;
            obstacle.transform.localScale = new Vector3((float)cellSize.x * 0.9f, 1.0f, (float)cellSize.y * 0.9f);
            obstacle.name = $"CombatObstacle_{gridPos.x}_{gridPos.y}";
            
            var renderer = obstacle.GetComponent<Renderer>();
            renderer.material.color = Color.gray;
        }

        private void CreateTargets()
        {
            var random = new Unity.Mathematics.Random(54321);
            var targetCount = 3; // Fewer targets for combat scenario
            
            for (int i = 0; i < targetCount; i++)
            {
                var targetEntity = entityManager.CreateEntity();
                
                Vector3 targetPos = GetTargetPosition(i, random);
                
                entityManager.AddComponentData(targetEntity, new LocalTransform
                {
                    Position = targetPos,
                    Rotation = quaternion.identity,
                    Scale = 1.0f
                });

                entityManager.AddComponentData(targetEntity, new SimulationTransform
                {
                    position = new dfloat3((dfloat)targetPos.x, (dfloat)targetPos.y, (dfloat)targetPos.z),
                    rotation = new dquaternion(dfloat.Zero, dfloat.Zero, dfloat.Zero, dfloat.One),
                    scale = dfloat.One
                });

                entityManager.AddComponentData(targetEntity, new FlowFieldTarget
                {
                    targetId = i + 1,
                    isActive = true
                });

                targetEntities.Add(targetEntity);

                if (i < flowFieldEntities.Count)
                {
                    var flowFieldGrid = entityManager.GetComponentData<FlowFieldGrid>(flowFieldEntities[i]);
                    flowFieldGrid.targetEntity = targetEntity;
                    flowFieldGrid.needsUpdate = true;
                    entityManager.SetComponentData(flowFieldEntities[i], flowFieldGrid);
                }

                if (enableCombatVisualization)
                {
                    CreateTargetVisual(targetPos, i);
                }
            }
        }

        private Vector3 GetTargetPosition(int targetIndex, Unity.Mathematics.Random random)
        {
            var worldWidth = gridSize.x * (float)cellSize.x;
            var worldHeight = gridSize.y * (float)cellSize.y;
            
            // Place targets strategically for combat testing
            switch (targetIndex)
            {
                case 0: return new Vector3(worldWidth * 0.8f, 1.0f, worldHeight * 0.5f);
                case 1: return new Vector3(worldWidth * 0.2f, 1.0f, worldHeight * 0.8f);
                case 2: return new Vector3(worldWidth * 0.5f, 1.0f, worldHeight * 0.2f);
                default: return new Vector3(worldWidth / 2, 1.0f, worldHeight / 2);
            }
        }

        private void CreateTargetVisual(Vector3 position, int targetIndex)
        {
            var target = GameObject.CreatePrimitive(PrimitiveType.Sphere);
            target.transform.position = position;
            target.transform.localScale = Vector3.one * 3.0f;
            target.name = $"CombatTarget_{targetIndex}";
            
            var renderer = target.GetComponent<Renderer>();
            renderer.material.color = Color.red;

            var light = target.AddComponent<Light>();
            light.type = LightType.Point;
            light.range = 15.0f;
            light.intensity = 3.0f;
            light.color = Color.red;
        }

        private void CreateTowers()
        {
            var random = new Unity.Mathematics.Random(98765);
            var worldWidth = gridSize.x * (float)cellSize.x;
            var worldHeight = gridSize.y * (float)cellSize.y;

            for (int i = 0; i < towerCount; i++)
            {
                var towerEntity = entityManager.CreateEntity();

                // Strategic tower placement
                Vector3 towerPos = GetTowerPosition(i, worldWidth, worldHeight, random);

                // Add transform components
                entityManager.AddComponentData(towerEntity, new LocalTransform
                {
                    Position = towerPos,
                    Rotation = quaternion.identity,
                    Scale = 1.0f
                });

                entityManager.AddComponentData(towerEntity, new SimulationTransform
                {
                    position = new dfloat3((dfloat)towerPos.x, (dfloat)towerPos.y, (dfloat)towerPos.z),
                    rotation = new dquaternion(dfloat.Zero, dfloat.Zero, dfloat.Zero, dfloat.One),
                    scale = dfloat.One
                });

                // Add unit stats for towers
                var towerStats = CreateTowerStats(i);
                entityManager.AddComponentData(towerEntity, towerStats);

                // Add combat components based on tower type
                AddTowerCombatComponents(towerEntity, i);

                towerEntities.Add(towerEntity);

                if (enableCombatVisualization)
                {
                    CreateTowerVisual(towerPos, i);
                }
            }
        }

        private Vector3 GetTowerPosition(int towerIndex, float worldWidth, float worldHeight, Unity.Mathematics.Random random)
        {
            // Create defensive lines and strategic positions
            var pattern = towerIndex % 4;

            switch (pattern)
            {
                case 0: // Front line defense
                    return new Vector3(
                        worldWidth * 0.6f + random.NextFloat(-10f, 10f),
                        1.0f,
                        worldHeight * (0.2f + (towerIndex * 0.15f) % 0.6f)
                    );
                case 1: // Side defense
                    return new Vector3(
                        worldWidth * (0.3f + (towerIndex * 0.1f) % 0.4f),
                        1.0f,
                        worldHeight * 0.7f + random.NextFloat(-5f, 5f)
                    );
                case 2: // Central support
                    return new Vector3(
                        worldWidth * 0.5f + random.NextFloat(-15f, 15f),
                        1.0f,
                        worldHeight * 0.5f + random.NextFloat(-15f, 15f)
                    );
                case 3: // Rear guard
                    return new Vector3(
                        worldWidth * 0.3f + random.NextFloat(-8f, 8f),
                        1.0f,
                        worldHeight * (0.1f + (towerIndex * 0.12f) % 0.3f)
                    );
                default:
                    return new Vector3(worldWidth / 2, 1.0f, worldHeight / 2);
            }
        }

        private UnitStats CreateTowerStats(int towerIndex)
        {
            var towerType = towerIndex % 5; // 5 different tower types

            switch (towerType)
            {
                case 0: // Basic Archer Tower
                    return new UnitStats
                    {
                        maxHealth = new dfloat(200),
                        currentHealth = new dfloat(200),
                        attackDamage = new dfloat(25),
                        attackRange = new dfloat(8),
                        attackCooldown = new dfloat(1.0f),
                        radius = new dfloat(1.0f),
                        mass = new dfloat(100),
                        height = new dfloat(3.0f)
                    };
                case 1: // AOE Mage Tower
                    return new UnitStats
                    {
                        maxHealth = new dfloat(150),
                        currentHealth = new dfloat(150),
                        attackDamage = new dfloat(40),
                        attackRange = new dfloat(6),
                        attackCooldown = new dfloat(2.0f),
                        radius = new dfloat(1.0f),
                        mass = new dfloat(100),
                        height = new dfloat(3.5f)
                    };
                case 2: // Rapid Fire Tower
                    return new UnitStats
                    {
                        maxHealth = new dfloat(120),
                        currentHealth = new dfloat(120),
                        attackDamage = new dfloat(15),
                        attackRange = new dfloat(7),
                        attackCooldown = new dfloat(0.5f),
                        radius = new dfloat(1.0f),
                        mass = new dfloat(100),
                        height = new dfloat(2.5f)
                    };
                case 3: // Chain Lightning Tower
                    return new UnitStats
                    {
                        maxHealth = new dfloat(180),
                        currentHealth = new dfloat(180),
                        attackDamage = new dfloat(30),
                        attackRange = new dfloat(9),
                        attackCooldown = new dfloat(1.5f),
                        radius = new dfloat(1.0f),
                        mass = new dfloat(100),
                        height = new dfloat(4.0f)
                    };
                case 4: // DOT Poison Tower
                    return new UnitStats
                    {
                        maxHealth = new dfloat(160),
                        currentHealth = new dfloat(160),
                        attackDamage = new dfloat(20),
                        attackRange = new dfloat(5),
                        attackCooldown = new dfloat(0.8f),
                        radius = new dfloat(1.0f),
                        mass = new dfloat(100),
                        height = new dfloat(3.0f)
                    };
                default:
                    return new UnitStats
                    {
                        maxHealth = new dfloat(100),
                        currentHealth = new dfloat(100),
                        attackDamage = new dfloat(20),
                        attackRange = new dfloat(5),
                        attackCooldown = new dfloat(1.0f),
                        radius = new dfloat(1.0f),
                        mass = new dfloat(100),
                        height = new dfloat(2.0f)
                    };
            }
        }

        private void AddTowerCombatComponents(Entity towerEntity, int towerIndex)
        {
            var towerType = towerIndex % 5;

            // Add basic attack data
            var attackData = CreateTowerAttackData(towerType);
            entityManager.AddComponentData(towerEntity, attackData);

            // Add targeting data
            var targetingData = CreateTowerTargetingData(towerType);
            entityManager.AddComponentData(towerEntity, targetingData);

            // Mark as friendly team
            entityManager.AddComponentData(towerEntity, new TeamData
            {
                teamId = FRIENDLY_TEAM,
                isHostileTo = ENEMY_TEAM
            });

            // Add unit stats initialization marker
            entityManager.AddComponentData(towerEntity, new UnitStatsInitialized());
        }

        private AttackData CreateTowerAttackData(int towerType)
        {
            var currentTime = new dfloat(Time.time);

            switch (towerType)
            {
                case 0: // Basic Archer Tower
                    return new AttackData
                    {
                        baseDamage = new dfloat(25),
                        attackRange = new dfloat(8),
                        attackCooldown = new dfloat(1.0f),
                        lastAttackTime = currentTime,
                        attackPattern = testSingleTarget ? AttackPattern.SingleTarget : AttackPattern.Linear,
                        projectileType = ProjectileType.Linear,
                        damageType = DamageType.Physical,
                        maxTargets = 1,
                        projectileSpeed = new dfloat(15),
                        criticalChance = new dfloat(0.1f),
                        criticalMultiplier = new dfloat(2.0f)
                    };
                case 1: // AOE Mage Tower
                    return new AttackData
                    {
                        baseDamage = new dfloat(40),
                        attackRange = new dfloat(6),
                        attackCooldown = new dfloat(2.0f),
                        lastAttackTime = currentTime,
                        attackPattern = testAOEAttacks ? AttackPattern.AOEPosition : AttackPattern.SingleTarget,
                        projectileType = ProjectileType.Arc,
                        damageType = DamageType.Magical,
                        aoeRadius = new dfloat(3),
                        maxTargets = 8,
                        projectileSpeed = new dfloat(10),
                        criticalChance = new dfloat(0.15f),
                        criticalMultiplier = new dfloat(1.8f)
                    };
                default:
                    return new AttackData
                    {
                        baseDamage = new dfloat(20),
                        attackRange = new dfloat(5),
                        attackCooldown = new dfloat(1.0f),
                        lastAttackTime = currentTime,
                        attackPattern = AttackPattern.SingleTarget,
                        projectileType = ProjectileType.Linear,
                        damageType = DamageType.Physical,
                        maxTargets = 1,
                        projectileSpeed = new dfloat(12)
                    };
            }
        }

        private TargetingData CreateTowerTargetingData(int towerType)
        {
            var currentTime = new dfloat(Time.time);

            switch (towerType)
            {
                case 0: // Basic Archer - Target nearest
                    return new TargetingData
                    {
                        strategy = TargetingStrategy.Nearest,
                        currentTarget = Entity.Null,
                        targetAcquiredTime = currentTime,
                        targetLostTime = currentTime,
                        targetLostDelay = new dfloat(0.5f),
                        retargetDelay = new dfloat(0.2f),
                        targetingRange = new dfloat(10.0f),
                        targetLossRange = new dfloat(12.0f),
                        canTargetAir = true,
                        canTargetGround = true,
                        canTargetStructures = false
                    };
                case 1: // AOE Mage - Target highest health for maximum AOE value
                    return new TargetingData
                    {
                        strategy = TargetingStrategy.HighestHealth,
                        currentTarget = Entity.Null,
                        targetAcquiredTime = currentTime,
                        targetLostTime = currentTime,
                        targetLostDelay = new dfloat(0.3f),
                        retargetDelay = new dfloat(0.5f),
                        targetingRange = new dfloat(8.0f),
                        targetLossRange = new dfloat(10.0f),
                        canTargetAir = true,
                        canTargetGround = true,
                        canTargetStructures = false,
                        maxSimultaneousTargets = 5,
                        targetSpreadRadius = new dfloat(3.0f)
                    };
                case 2: // Rapid Fire - Target first (furthest along path)
                    return new TargetingData
                    {
                        strategy = TargetingStrategy.First,
                        currentTarget = Entity.Null,
                        targetAcquiredTime = currentTime,
                        targetLostTime = currentTime,
                        targetLostDelay = new dfloat(0.1f),
                        retargetDelay = new dfloat(0.1f),
                        targetingRange = new dfloat(12.0f),
                        targetLossRange = new dfloat(14.0f),
                        canTargetAir = true,
                        canTargetGround = true,
                        canTargetStructures = false
                    };
                case 3: // Chain Lightning - Target with most nearby enemies
                    return new TargetingData
                    {
                        strategy = TargetingStrategy.Strongest,
                        currentTarget = Entity.Null,
                        targetAcquiredTime = currentTime,
                        targetLostTime = currentTime,
                        targetLostDelay = new dfloat(0.4f),
                        retargetDelay = new dfloat(0.3f),
                        targetingRange = new dfloat(9.0f),
                        targetLossRange = new dfloat(11.0f),
                        canTargetAir = true,
                        canTargetGround = true,
                        canTargetStructures = false,
                        maxSimultaneousTargets = 3,
                        targetSpreadRadius = new dfloat(4.0f)
                    };
                case 4: // DOT Poison - Target lowest health to finish off
                    return new TargetingData
                    {
                        strategy = TargetingStrategy.LowestHealth,
                        currentTarget = Entity.Null,
                        targetAcquiredTime = currentTime,
                        targetLostTime = currentTime,
                        targetLostDelay = new dfloat(0.2f),
                        retargetDelay = new dfloat(0.4f),
                        targetingRange = new dfloat(7.0f),
                        targetLossRange = new dfloat(9.0f),
                        canTargetAir = false,
                        canTargetGround = true,
                        canTargetStructures = false,
                        usePrediction = true,
                        predictionTime = new dfloat(0.5f)
                    };
                default:
                    return new TargetingData
                    {
                        strategy = TargetingStrategy.Nearest,
                        currentTarget = Entity.Null,
                        targetAcquiredTime = currentTime,
                        targetLostTime = currentTime,
                        targetLostDelay = new dfloat(0.5f),
                        retargetDelay = new dfloat(0.2f),
                        targetingRange = new dfloat(10.0f),
                        targetLossRange = new dfloat(12.0f),
                        canTargetAir = true,
                        canTargetGround = true,
                        canTargetStructures = false
                    };
            }
        }

        private void CreateTowerVisual(Vector3 position, int towerIndex)
        {
            var towerType = towerIndex % 5;
            var tower = GameObject.CreatePrimitive(PrimitiveType.Cylinder);
            tower.transform.position = position;
            tower.transform.localScale = new Vector3(2.0f, 1.5f, 2.0f);
            tower.name = $"Tower_{GetTowerTypeName(towerType)}_{towerIndex}";

            var renderer = tower.GetComponent<Renderer>();
            if (towerMaterial != null)
            {
                renderer.material = towerMaterial;
            }
            else
            {
                renderer.material.color = GetTowerColor(towerType);
            }

            // Add range indicator
            var rangeIndicator = GameObject.CreatePrimitive(PrimitiveType.Cylinder);
            rangeIndicator.transform.parent = tower.transform;
            rangeIndicator.transform.localPosition = Vector3.zero;
            rangeIndicator.transform.localScale = new Vector3(GetTowerRange(towerType) * 2, 0.01f, GetTowerRange(towerType) * 2);
            rangeIndicator.name = "RangeIndicator";

            var rangeRenderer = rangeIndicator.GetComponent<Renderer>();
            rangeRenderer.material.color = new Color(1, 1, 1, 0.1f);
            rangeRenderer.material.SetFloat("_Mode", 3); // Transparent mode
        }

        private string GetTowerTypeName(int towerType)
        {
            switch (towerType)
            {
                case 0: return "Archer";
                case 1: return "Mage";
                case 2: return "RapidFire";
                case 3: return "Lightning";
                case 4: return "Poison";
                default: return "Basic";
            }
        }

        private Color GetTowerColor(int towerType)
        {
            switch (towerType)
            {
                case 0: return Color.brown;      // Archer
                case 1: return Color.blue;       // Mage
                case 2: return Color.yellow;     // Rapid Fire
                case 3: return Color.cyan;       // Lightning
                case 4: return Color.green;      // Poison
                default: return Color.gray;
            }
        }

        private float GetTowerRange(int towerType)
        {
            switch (towerType)
            {
                case 0: return 8.0f;   // Archer
                case 1: return 6.0f;   // Mage
                case 2: return 7.0f;   // Rapid Fire
                case 3: return 9.0f;   // Lightning
                case 4: return 5.0f;   // Poison
                default: return 5.0f;
            }
        }

        private void SetupSpawners()
        {
            var random = new Unity.Mathematics.Random(13579);
            var worldWidth = gridSize.x * (float)cellSize.x;
            var worldHeight = gridSize.y * (float)cellSize.y;

            for (int i = 0; i < spawnerCount; i++)
            {
                Vector3 spawnerPos = GetSpawnerPosition(i, worldWidth, worldHeight, random);
                spawnerPositions.Add(spawnerPos);
                spawnerUnitCounts[i] = 0;

                if (enableCombatVisualization)
                {
                    CreateSpawnerVisual(spawnerPos, i);
                }
            }
        }

        private Vector3 GetSpawnerPosition(int spawnerIndex, float worldWidth, float worldHeight, Unity.Mathematics.Random random)
        {
            // Place spawners on the opposite side from towers (enemy spawn points)
            var angle = (float)spawnerIndex / spawnerCount * math.PI; // Half circle on left side
            var radius = math.min(worldWidth, worldHeight) * 0.3f;

            return new Vector3(
                worldWidth * 0.1f + radius * math.cos(angle),
                0.5f,
                worldHeight * 0.5f + radius * math.sin(angle)
            );
        }

        private void CreateSpawnerVisual(Vector3 position, int spawnerIndex)
        {
            var spawner = GameObject.CreatePrimitive(PrimitiveType.Cube);
            spawner.transform.position = position;
            spawner.transform.localScale = new Vector3(3.0f, 1.0f, 3.0f);
            spawner.name = $"EnemySpawner_{spawnerIndex}";

            var renderer = spawner.GetComponent<Renderer>();
            renderer.material.color = Color.red;

            // Add warning light
            var light = spawner.AddComponent<Light>();
            light.type = LightType.Point;
            light.range = 8.0f;
            light.intensity = 1.0f;
            light.color = Color.red;
        }

        private IEnumerator PeriodicUnitSpawning()
        {
            while (true)
            {
                yield return new WaitForSeconds(spawnInterval);

                if (combatUnitTypes.Count > 0)
                {
                    SpawnEnemyUnit();
                    currentSpawnerIndex = (currentSpawnerIndex + 1) % spawnerCount;
                }
            }
        }

        private void SpawnEnemyUnit()
        {
            if (spawnerUnitCounts[currentSpawnerIndex] >= maxUnitsPerSpawner)
            {
                return;
            }

            var spawnerPos = spawnerPositions[currentSpawnerIndex];
            var unitType = combatUnitTypes[UnityEngine.Random.Range(0, combatUnitTypes.Count)];

            var unitEntity = entityManager.CreateEntity();

            // Add transform components
            entityManager.AddComponentData(unitEntity, new LocalTransform
            {
                Position = spawnerPos,
                Rotation = quaternion.identity,
                Scale = 1.0f
            });

            entityManager.AddComponentData(unitEntity, new SimulationTransform
            {
                position = new dfloat3((dfloat)spawnerPos.x, (dfloat)spawnerPos.y, (dfloat)spawnerPos.z),
                rotation = new dquaternion(dfloat.Zero, dfloat.Zero, dfloat.Zero, dfloat.One),
                scale = dfloat.One
            });

            // Add unit type and enhanced stats for combat
            entityManager.AddComponentData(unitEntity, new EnemyDefinitionRef
            {
                EnemyDefinitionId = unitType.Id
            });

            var unitStats = CreateEnemyUnitStats(unitType);
            entityManager.AddComponentData(unitEntity, unitStats);

            // Add flow field follower
            var movementType = unitType.GetMovementType();
            var targetId = UnityEngine.Random.Range(1, targetEntities.Count + 1);

            entityManager.AddComponentData(unitEntity, new FlowFieldFollower
            {
                movementType = movementType,
                maxSpeed = unitStats.maxSpeed,
                minSpeed = new dfloat(0.5f),
                acceleration = unitStats.acceleration,
                deceleration = unitStats.deceleration,
                flowFieldStrength = new dfloat(1.0f),
                avoidanceRadius = new dfloat(unitType.avoidanceRadius),
                separationStrength = new dfloat(unitType.separationStrength),
                cohesionStrength = new dfloat(unitType.cohesionStrength),
                alignmentStrength = new dfloat(unitType.alignmentStrength),
                obstacleAvoidanceStrength = new dfloat(unitType.obstacleAvoidanceStrength),
                avoidanceLayer = 0,
                useAvoidance = unitType.useAvoidance,
                targetProximityThreshold = new dfloat(unitType.targetProximityThreshold)
            });

            entityManager.AddComponentData(unitEntity, new MovementTarget
            {
                targetId = targetId
            });

            // Add avoidance if enabled
            var avoidanceData = unitType.ToAvoidanceData();
            if (avoidanceData.HasValue)
            {
                entityManager.AddComponentData(unitEntity, avoidanceData.Value);
            }

            // Add enemy team data
            entityManager.AddComponentData(unitEntity, new TeamData
            {
                teamId = ENEMY_TEAM,
                isHostileTo = FRIENDLY_TEAM
            });

            // Add combat capabilities to some units
            if (enableAdvancedCombat && UnityEngine.Random.value < 0.3f) // 30% of units have combat abilities
            {
                AddEnemyCombatComponents(unitEntity, unitStats);
            }

            enemyUnits.Add(unitEntity);
            spawnerUnitCounts[currentSpawnerIndex]++;

            if (enableCombatVisualization)
            {
                CreateEnemyUnitVisual(unitEntity, spawnerPos, unitType);
            }

            Debug.Log($"Spawned enemy {unitType.Name} at spawner {currentSpawnerIndex}, targeting {targetId}");
        }

        private UnitStats CreateEnemyUnitStats(EnemyDefinition unitType)
        {
            var baseStats = unitType.ToUnitStats();

            // Enhance stats for combat scenario
            return new UnitStats
            {
                maxSpeed = baseStats.maxSpeed,
                acceleration = baseStats.acceleration,
                deceleration = baseStats.deceleration,
                rotationSpeed = baseStats.rotationSpeed,
                maxHealth = baseStats.maxHealth * new dfloat(1.5f), // More health for longer combat
                currentHealth = baseStats.maxHealth * new dfloat(1.5f),
                attackDamage = baseStats.attackDamage * new dfloat(0.8f), // Slightly less damage
                attackRange = baseStats.attackRange,
                attackCooldown = baseStats.attackCooldown * new dfloat(1.2f), // Slower attacks
                radius = baseStats.radius,
                mass = baseStats.mass,
                height = baseStats.height,
                abilityFlags = baseStats.abilityFlags
            };
        }

        private void AddEnemyCombatComponents(Entity unitEntity, UnitStats unitStats)
        {
            var currentTime = new dfloat(Time.time);

            // Add basic attack capability
            var attackData = new AttackData
            {
                baseDamage = unitStats.attackDamage,
                attackRange = unitStats.attackRange,
                attackCooldown = unitStats.attackCooldown,
                lastAttackTime = currentTime,
                attackPattern = AttackPattern.SingleTarget,
                projectileType = ProjectileType.Instant,
                damageType = DamageType.Physical,
                maxTargets = 1,
                criticalChance = new dfloat(0.05f),
                criticalMultiplier = new dfloat(1.5f)
            };

            entityManager.AddComponentData(unitEntity, attackData);

            // Add targeting data
            var targetingData = new TargetingData
            {
                strategy = TargetingStrategy.Nearest,
                currentTarget = Entity.Null,
                targetAcquiredTime = currentTime,
                targetLostTime = currentTime,
                targetLostDelay = new dfloat(0.5f),
                retargetDelay = new dfloat(0.3f),
                //lastRetargetTime = currentTime
            };

            entityManager.AddComponentData(unitEntity, targetingData);
        }

        private void CreateEnemyUnitVisual(Entity unitEntity, Vector3 position, EnemyDefinition unitType)
        {
            GameObject unitVisual;

            if (unitType.Prefab != null)
            {
                unitVisual = Instantiate(unitType.Prefab, position, Quaternion.identity);
            }
            else
            {
                unitVisual = GameObject.CreatePrimitive(PrimitiveType.Capsule);
                unitVisual.transform.position = position;
                unitVisual.transform.localScale = Vector3.one * unitType.radius * 1.2f;
            }

            unitVisual.name = $"Enemy_{unitType.Name}_{unitEntity.Index}";

            var renderer = unitVisual.GetComponent<Renderer>();
            if (renderer != null)
            {
                if (enemyUnitMaterial != null)
                {
                    renderer.material = enemyUnitMaterial;
                }
                else
                {
                    renderer.material.color = Color.red;
                }
            }
        }

        private IEnumerator CombatStatistics()
        {
            while (true)
            {
                yield return new WaitForSeconds(5.0f); // Update every 5 seconds

                DisplayCombatStatistics();
            }
        }

        private void DisplayCombatStatistics()
        {
            var activeEnemies = 0;
            var activeTowers = 0;
            var totalEnemyHealth = 0f;
            var totalTowerHealth = 0f;

            foreach (var enemy in enemyUnits)
            {
                if (entityManager.Exists(enemy))
                {
                    activeEnemies++;
                    if (entityManager.HasComponent<UnitStats>(enemy))
                    {
                        var stats = entityManager.GetComponentData<UnitStats>(enemy);
                        totalEnemyHealth += (float)stats.currentHealth;
                    }
                }
            }

            foreach (var tower in towerEntities)
            {
                if (entityManager.Exists(tower))
                {
                    activeTowers++;
                    if (entityManager.HasComponent<UnitStats>(tower))
                    {
                        var stats = entityManager.GetComponentData<UnitStats>(tower);
                        totalTowerHealth += (float)stats.currentHealth;
                    }
                }
            }

            Debug.Log($"=== COMBAT STATISTICS ===");
            Debug.Log($"Active Enemies: {activeEnemies} | Active Towers: {activeTowers}");
            Debug.Log($"Total Enemy Health: {totalEnemyHealth:F0} | Total Tower Health: {totalTowerHealth:F0}");
            Debug.Log($"Total Kills: {totalKills} | Total Damage Dealt: {totalDamageDealt}");
            Debug.Log($"==========================");
        }

        void Update()
        {
            if (Time.frameCount % 120 == 0) // Every 2 seconds at 60 FPS
            {
                CleanupDestroyedUnits();
            }
        }

        private void CleanupDestroyedUnits()
        {
            // Remove destroyed enemies from tracking
            for (int i = enemyUnits.Count - 1; i >= 0; i--)
            {
                if (!entityManager.Exists(enemyUnits[i]))
                {
                    totalKills++;
                    enemyUnits.RemoveAt(i);
                }
            }
        }

        void OnDestroy()
        {
            // Cleanup all spawned entities
            foreach (var unit in enemyUnits)
            {
                if (entityManager.Exists(unit))
                {
                    entityManager.DestroyEntity(unit);
                }
            }

            foreach (var tower in towerEntities)
            {
                if (entityManager.Exists(tower))
                {
                    entityManager.DestroyEntity(tower);
                }
            }

            foreach (var target in targetEntities)
            {
                if (entityManager.Exists(target))
                {
                    entityManager.DestroyEntity(target);
                }
            }

            foreach (var flowField in flowFieldEntities)
            {
                if (entityManager.Exists(flowField))
                {
                    entityManager.DestroyEntity(flowField);
                }
            }
        }

        /// <summary>
        /// Initialize the new modular visualization system for combat scenarios
        /// </summary>
        private void InitializeVisualizationComponents()
        {
            // Create and configure Grid Visualization
            if (enableGridVisualization && flowFieldEntities.Count > 0)
            {
                gridVisualization = gameObject.AddComponent<FlowField.Visualization.GridVisualization>();
                gridVisualization.Initialize(entityManager, world);
                gridVisualization.FlowFieldEntity = flowFieldEntities[0]; // Use first flow field
                gridVisualization.GridSize = gridSize;
                gridVisualization.CellSize = cellSize;
                gridVisualization.WorldOrigin = worldOrigin;
                gridVisualization.MovementType = FlowField.MovementType.Ground;
                gridVisualization.IsEnabled = true;
            }

            // Create and configure Unit Visualization for all units (friendly, enemy, towers)
            if (enableUnitVisualization)
            {
                unitVisualization = gameObject.AddComponent<FlowField.Visualization.UnitVisualization>();
                unitVisualization.Initialize(entityManager, world);

                // Combine all unit lists for visualization
                var allUnits = new List<Entity>();
                allUnits.AddRange(friendlyUnits);
                allUnits.AddRange(enemyUnits);
                allUnits.AddRange(towerEntities);

                unitVisualization.UnitsToVisualize = allUnits;
                unitVisualization.AutoFindUnits = true;
                unitVisualization.IsEnabled = true;

                if (targetEntities.Count > 0)
                {
                    unitVisualization.TargetEntity = targetEntities[0];
                }
            }

            // Create and configure Target Visualization
            if (enableTargetVisualization)
            {
                targetVisualization = gameObject.AddComponent<FlowField.Visualization.TargetVisualization>();
                targetVisualization.Initialize(entityManager, world);
                targetVisualization.TargetsToVisualize = targetEntities;
                targetVisualization.AutoFindTargets = true;
                targetVisualization.IsEnabled = true;
            }

            // Create and configure Projectile Visualization
            if (enableProjectileVisualization)
            {
                projectileVisualization = gameObject.AddComponent<FlowField.Visualization.ProjectileVisualization>();
                projectileVisualization.Initialize(entityManager, world);
                projectileVisualization.AutoFindProjectiles = true;
                projectileVisualization.IsEnabled = true;
            }

            Debug.Log("Modular visualization system initialized for Complex Combat Test Scenario");
        }
    }
}
