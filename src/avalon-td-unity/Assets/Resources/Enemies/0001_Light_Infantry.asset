%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: e92add1fa8f03457da0da0991dee5580, type: 3}
  m_Name: 0001_Light_Infantry
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects:
    - {fileID: 1276112760387413235, guid: b7e203312541747fcbda22300fd6e97e, type: 3}
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: Id
      Entry: 3
      Data: 1
    - Name: Name
      Entry: 1
      Data: Light_Infantry
    - Name: SafeName
      Entry: 1
      Data: 0001_Light_Infantry
    - Name: Prefab
      Entry: 10
      Data: 0
  MinSpeed:
    m_rawValue: 0
  MaxSpeed:
    m_rawValue: 0
  MaxHealth:
    m_rawValue: 0
  maxSpeed: 5
  minSpeed: 0.5
  acceleration: 10
  deceleration: 8
  rotationSpeed: 5
  radius: 0.5
  mass: 1
  height: 1
  maxHealth: 100
  attackDamage: 10
  attackRange: 1.5
  attackCooldown: 1
  useAvoidance: 1
  avoidanceRadius: 1.5
  separationStrength: 2
  cohesionStrength: 0.5
  alignmentStrength: 0.3
  obstacleAvoidanceStrength: 3
  targetProximityThreshold: 2
  visualScale: 1
  visualOffset: {x: 0, y: 0, z: 0}
  canFly: 0
  canSwim: 0
  isHeavy: 0
  hasSpecialAbility1: 0
  hasSpecialAbility2: 0
  hasSpecialAbility3: 0
