using Avalon.Simulation.Movement;
using Unity.Burst;
using Unity.Collections;
using Unity.Entities;
using Unity.Jobs;

namespace FlowField
{
    [BurstCompile]
    public struct PopulateNeighborDataJob : IJobParallelFor
    {
        [ReadOnly] public NativeArray<Entity> entities;
        [ReadOnly] public ComponentLookup<SimulationTransform> transformLookup;
        [ReadOnly] public ComponentLookup<FlowFieldFollower> followerLookup;
        [ReadOnly] public ComponentLookup<AvoidanceData> avoidanceDataLookup;
        [NativeDisableParallelForRestriction] public NativeArray<NeighborData> neighborData;

        public void Execute(int index)
        {
            var unit = entities[index];
            var neighbor = new NeighborData { isValid = false };

            if (transformLookup.HasComponent(unit) &&
                followerLookup.HasComponent(unit) &&
                avoidanceDataLookup.HasComponent(unit))
            {
                var transform = transformLookup[unit];
                var follower = followerLookup[unit];
                var avoidanceData = avoidanceDataLookup[unit];

                neighbor.position = transform.position;
                neighbor.velocity = avoidanceData.velocity;
                neighbor.avoidanceLayer = follower.avoidanceLayer;
                neighbor.radius = avoidanceData.radius;
                neighbor.isValid = true;
            }

            neighborData[index] = neighbor;
        }
    }
}