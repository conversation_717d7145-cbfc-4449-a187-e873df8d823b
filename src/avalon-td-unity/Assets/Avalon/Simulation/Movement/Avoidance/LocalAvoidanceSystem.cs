using FlowField;
using Unity.Burst;
using Unity.Collections;
using Unity.Entities;
using Unity.Jobs;
using Unity.Mathematics;

namespace Avalon.Simulation.Movement.Avoidance
{
    [BurstCompile]
    [UpdateInGroup(typeof(MovementSystemGroup))]
    [UpdateBefore(typeof(FlowFieldSimpleMovementSystem))]
    public partial struct LocalAvoidanceSystem : ISystem
    {
        private BufferLookup<FlowFieldCellBuffer> flowFieldLookup;
        private ComponentLookup<FlowFieldGrid> flowFieldGridLookup;
        private EntityQuery flowFieldQuery;

        // For neighbor detection (DETERMINISTIC)
        private ComponentLookup<SimulationTransform> transformLookup;
        private ComponentLookup<AvoidanceData> avoidanceDataLookup;
        private ComponentLookup<FlowFieldFollower> followerLookup;
        private EntityQuery unitQuery;

        // For target proximity detection (separate lookup to avoid aliasing) (DETERMINISTIC)
        private ComponentLookup<SimulationTransform> targetTransformLookup;

        // Memory pool for neighbor data to reduce allocations
        private NativeArray<NeighborData> neighborDataPool;
        private bool poolInitialized;

        private EntityQuery fixedTimestepQuery;

        [BurstCompile]
        public void OnCreate(ref SystemState state)
        {
            flowFieldLookup = state.GetBufferLookup<FlowFieldCellBuffer>(true);
            flowFieldGridLookup = state.GetComponentLookup<FlowFieldGrid>(true);
            transformLookup = state.GetComponentLookup<SimulationTransform>(true);
            avoidanceDataLookup = state.GetComponentLookup<AvoidanceData>(true);
            followerLookup = state.GetComponentLookup<FlowFieldFollower>(true);
            targetTransformLookup = state.GetComponentLookup<SimulationTransform>(true);

            // Create query for flow field entities
            flowFieldQuery = state.GetEntityQuery(typeof(FlowFieldGrid));

            // Create query for all units (for neighbor detection) - must match job requirements
            // Include FlowFieldFollower as ReadOnly to establish proper dependency tracking
            unitQuery = state.GetEntityQuery(new EntityQueryDesc
            {
                All = new ComponentType[]
                {
                    ComponentType.ReadOnly<SimulationTransform>(),
                    ComponentType.ReadOnly<FlowFieldFollower>(),
                    ComponentType.ReadOnly<AvoidanceData>()
                }
            });

            // Create query for fixed timestep
            fixedTimestepQuery = state.GetEntityQuery(typeof(FixedTimestep));

            // Require fixed timestep
            state.RequireForUpdate<FixedTimestep>();

            // Explicitly require FlowFieldFollower to establish dependency tracking
            state.RequireForUpdate<FlowFieldFollower>();

            // Initialize memory pool
            poolInitialized = false;
        }

        [BurstCompile]
        public void OnDestroy(ref SystemState state)
        {
            // Clean up memory pool safely to prevent crashes
            if (poolInitialized && neighborDataPool.IsCreated)
            {
            
                neighborDataPool.Dispose();
                
                poolInitialized = false;
            }
        }

        [BurstCompile]
        public void OnUpdate(ref SystemState state)
        {
            // Get fixed timestep singleton
            if (fixedTimestepQuery.IsEmpty) return;
            var fixedTimestep = fixedTimestepQuery.GetSingleton<FixedTimestep>();

            // Only process on fixed ticks
            if (!fixedTimestep.shouldProcessTick) return;

            // Update lookups to ensure they have the latest component data
            // This is critical for proper dependency tracking
            flowFieldLookup.Update(ref state);
            flowFieldGridLookup.Update(ref state);
            transformLookup.Update(ref state);
            avoidanceDataLookup.Update(ref state);
            followerLookup.Update(ref state);
            targetTransformLookup.Update(ref state);

            // Get all flow field entities
            var flowFieldEntities = flowFieldQuery.ToEntityArray(Allocator.TempJob);

            // Get all units for neighbor detection
            var allUnits = unitQuery.ToEntityArray(Allocator.TempJob);

            // Use memory pool for neighbor data to reduce allocations
            // Add safety checks to prevent crashes
            if (!poolInitialized || !neighborDataPool.IsCreated || neighborDataPool.Length < allUnits.Length)
            {
                // Safely dispose existing pool
                if (poolInitialized && neighborDataPool.IsCreated)
                {
                    neighborDataPool.Dispose();
                }

                // Create new pool with safety checks
                int poolSize = math.max(allUnits.Length, 1024);
                if (poolSize > 0)
                {
                    neighborDataPool = new NativeArray<NeighborData>(poolSize, Allocator.Persistent);
                    poolInitialized = true;
                }
            }

            // Get a slice of the pool for current frame
            var neighborData = neighborDataPool.GetSubArray(0, allUnits.Length);

            // Populate neighbor data with burst-compiled job for better performance
            var populateNeighborJob = new PopulateNeighborDataJob
            {
                entities = allUnits,
                transformLookup = transformLookup,
                followerLookup = followerLookup,
                avoidanceDataLookup = avoidanceDataLookup,
                neighborData = neighborData
            };

            // Schedule neighbor data population job and assign to dependency
            // This ensures proper dependency tracking for FlowFieldFollower reads
            state.Dependency = populateNeighborJob.Schedule(allUnits.Length, 64, state.Dependency);

            // Create target data array to avoid aliasing with LocalTransform
            var targetData = new NativeArray<TargetData>(flowFieldEntities.Length, Allocator.TempJob);

            // Populate target data
            for (int i = 0; i < flowFieldEntities.Length; i++)
            {
                var entity = flowFieldEntities[i];
                var target = new TargetData { isValid = false };

                if (flowFieldGridLookup.HasComponent(entity))
                {
                    var gridData = flowFieldGridLookup[entity];
                    if (targetTransformLookup.HasComponent(gridData.targetEntity))
                    {
                        var targetTransform = targetTransformLookup[gridData.targetEntity];
                        target.targetId = gridData.targetId;
                        target.targetPosition = targetTransform.position;
                        target.isValid = true;
                    }
                }

                targetData[i] = target;
            }

            // Calculate avoidance for units that need it
            var avoidanceJob = new CalculateAvoidanceJob
            {
                deltaTime = fixedTimestep.tickDuration,
                currentTime = fixedTimestep.currentTime,
                flowFieldLookup = flowFieldLookup,
                flowFieldGridLookup = flowFieldGridLookup,
                flowFieldEntities = flowFieldEntities,
                neighborData = neighborData,
                targetData = targetData,
            };

            // Chain job dependencies properly
            state.Dependency = avoidanceJob.ScheduleParallel(state.Dependency);

            // Dispose the temporary arrays after the job completes (neighborData is pooled, don't dispose)
            state.Dependency = flowFieldEntities.Dispose(state.Dependency);
            state.Dependency = allUnits.Dispose(state.Dependency);
            state.Dependency = targetData.Dispose(state.Dependency);
        }
    }
}