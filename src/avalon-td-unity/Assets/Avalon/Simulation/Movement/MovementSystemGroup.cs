﻿using Avalon.Simulation.Movement.Avoidance;
using FlowField;
using Unity.Entities;

namespace Avalon.Simulation.Movement
{
    [UpdateInGroup(typeof(FlowFieldSystemGroup))]
    [UpdateAfter(typeof(FlowFieldManagerSystem))]
    [WorldSystemFilter(WorldSystemFilterFlags.Default | WorldSystemFilterFlags.EntitySceneOptimizations)]
    public partial class MovementSystemGroup : ComponentSystemGroup
    {
        protected override void OnCreate()
        {
            base.OnCreate();

            // Ensure systems run in proper order
            var avoidanceSystem = World.GetOrCreateSystem<LocalAvoidanceSystem>();
            var simpleSystem = World.GetOrCreateSystem<FlowFieldSimpleMovementSystem>();
            var storePreviousSystem = World.GetOrCreateSystem<StorePreviousTransformSystem>();

            AddSystemToUpdateList(avoidanceSystem);
            AddSystemToUpdateList(simpleSystem);
            AddSystemToUpdateList(storePreviousSystem);
        }
    }
}