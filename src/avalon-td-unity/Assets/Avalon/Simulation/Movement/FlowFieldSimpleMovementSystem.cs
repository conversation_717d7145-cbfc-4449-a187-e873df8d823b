using Avalon.Simulation;
using Avalon.Simulation.Movement;
using Avalon.Simulation.Movement.Avoidance;
using Unity.Burst;
using Unity.Collections;
using Unity.Entities;

namespace FlowField
{
    [BurstCompile]
    [UpdateInGroup(typeof(MovementSystemGroup))]
    [UpdateAfter(typeof(LocalAvoidanceSystem))]
    public partial struct FlowFieldSimpleMovementSystem : ISystem
    {
        private BufferLookup<FlowFieldCellBuffer> flowFieldLookup;
        private ComponentLookup<FlowFieldGrid> flowFieldGridLookup;
        private EntityQuery flowFieldQuery;
        private EntityQuery fixedTimestepQuery;

        // For target overshoot prevention (DETERMINISTIC)
        private ComponentLookup<SimulationTransform> targetTransformLookup;

        [BurstCompile]
        public void OnCreate(ref SystemState state)
        {
            flowFieldLookup = state.GetBufferLookup<FlowFieldCellBuffer>(true);
            flowFieldGridLookup = state.GetComponentLookup<FlowFieldGrid>(true);
            targetTransformLookup = state.GetComponentLookup<SimulationTransform>(true);

            // Create query for flow field entities
            flowFieldQuery = state.GetEntityQuery(typeof(FlowFieldGrid));

            // Create query for fixed timestep
            fixedTimestepQuery = state.GetEntityQuery(typeof(FixedTimestep));

            // Require fixed timestep
            state.RequireForUpdate<FixedTimestep>();
        }

        [BurstCompile]
        public void OnUpdate(ref SystemState state)
        {
            // Get fixed timestep singleton
            if (fixedTimestepQuery.IsEmpty) return;
            var fixedTimestep = fixedTimestepQuery.GetSingleton<FixedTimestep>();

            // Only process on fixed ticks
            if (!fixedTimestep.shouldProcessTick) return;

            // Complete any previous jobs that might be writing to LocalTransform
            // This ensures we can safely read from targetTransformLookup
            state.Dependency.Complete();

            // Update lookups after ensuring dependencies are complete
            flowFieldLookup.Update(ref state);
            flowFieldGridLookup.Update(ref state);
            targetTransformLookup.Update(ref state);

            // Get all flow field entities
            var flowFieldEntities = flowFieldQuery.ToEntityArray(Allocator.TempJob);

            // Create target data array for overshoot prevention
            var targetData = new NativeArray<TargetData>(flowFieldEntities.Length, Allocator.TempJob);

            // Populate target data
            for (int i = 0; i < flowFieldEntities.Length; i++)
            {
                var entity = flowFieldEntities[i];
                var target = new TargetData { isValid = false };

                if (flowFieldGridLookup.HasComponent(entity))
                {
                    var gridData = flowFieldGridLookup[entity];
                    if (targetTransformLookup.HasComponent(gridData.targetEntity))
                    {
                        var targetTransform = targetTransformLookup[gridData.targetEntity];
                        target.targetId = gridData.targetId;
                        target.targetPosition = targetTransform.position;
                        target.isValid = true;
                    }
                }

                targetData[i] = target;
            }

            // Simple movement for units without avoidance
            var movementJob = new SimpleMovementJob
            {
                deltaTime = fixedTimestep.tickDuration,
                flowFieldLookup = flowFieldLookup,
                flowFieldGridLookup = flowFieldGridLookup,
                flowFieldEntities = flowFieldEntities,
                targetData = targetData
            };

            state.Dependency = movementJob.ScheduleParallel(state.Dependency);

            // Dispose the arrays after the job completes
            state.Dependency = flowFieldEntities.Dispose(state.Dependency);
            state.Dependency = targetData.Dispose(state.Dependency);
        }
    }
}