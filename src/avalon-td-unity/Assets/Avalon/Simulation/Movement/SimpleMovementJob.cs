using Avalon.Simulation.Movement;
using Unity.Burst;
using Unity.Collections;
using Unity.Deterministic.Mathematics;
using Unity.Entities;
using Unity.Mathematics;

namespace FlowField
{
    [BurstCompile]
    public partial struct SimpleMovementJob : IJobEntity
    {
        public dfloat deltaTime;
        [ReadOnly] public BufferLookup<FlowFieldCellBuffer> flowFieldLookup;
        [ReadOnly] public ComponentLookup<FlowFieldGrid> flowFieldGridLookup;
        [ReadOnly] public NativeArray<Entity> flowFieldEntities;

        // For target overshoot prevention
        [ReadOnly] public NativeArray<TargetData> targetData;

        public void Execute(ref SimulationTransform transform, ref FlowFieldFollower follower, ref MovementTarget movementTarget, ref Movement movement)
        {
            // Only process units without avoidance
            if (follower.useAvoidance) return;
            if (movementTarget.targetId == null) return;

            // Cache frequently used values for efficiency (DETERMINISTIC)
            // Map 3D world position (x,y,z) to flow field coordinates (x,z)
            var currentPosition = new dfloat2(transform.position.x, transform.position.z);
            var targetId = movementTarget.targetId.Value;
            var proximityThreshold = follower.targetProximityThreshold;

            // Get flow field direction using proper lookup
            var flowDirection = GetFlowFieldDirection(currentPosition, targetId, follower.movementType);

            // Use direct target direction when very close to ensure units reach the exact target
            var directTargetDirection = GetDirectTargetDirection(currentPosition, targetId, proximityThreshold);
            var directTargetMagnitude = dmath.Sqrt(directTargetDirection.x * directTargetDirection.x +
                                                   directTargetDirection.y * directTargetDirection.y);
            if (directTargetMagnitude > dfloat.Zero)
            {
                // Blend between flow field and direct target direction based on distance
                var distanceToTarget = GetDistanceToTarget(currentPosition, targetId);
                var blendFactor = dmath.min(proximityThreshold / dmath.max(distanceToTarget, new dfloat(0.01f)),
                    dfloat.One);
                flowDirection = flowDirection * (dfloat.One - blendFactor) + directTargetDirection * blendFactor;
            }

            // Simple velocity calculation with minimum speed
            var desiredVelocity = flowDirection * follower.maxSpeed;

            // Ensure minimum speed if there's a valid direction
            var directionMagnitude = dmath.Sqrt(flowDirection.x * flowDirection.x + flowDirection.y * flowDirection.y);
            if (directionMagnitude > dfloat.Zero)
            {
                var speed = dmath.max(follower.minSpeed, follower.maxSpeed);
                desiredVelocity = flowDirection * speed;
            }

            // Apply movement with target overshoot prevention
            var movementVector = desiredVelocity * deltaTime;

            // Prevent overshooting the target
            movementVector = PreventTargetOvershoot(currentPosition, movementVector, targetId, proximityThreshold);
            movement.Vector = movementVector;
            
            // Map flow field movement (x,y) to 3D world movement (x,0,z)
            var newPosition = transform.position + new dfloat3(movementVector.x, dfloat.Zero, movementVector.y);

            // Check if the new position is walkable (map 3D position to flow field coordinates for walkability check)
            if (IsPositionWalkable(new float2((float)newPosition.x, (float)newPosition.z), follower.movementType))
            {
                transform.position = newPosition;

                // Update rotation to face movement direction if moving (DETERMINISTIC)
                if (dmath.lengthsq(movementVector) > dfloat.Zero)
                {
                    // Convert 2D movement vector to 3D forward direction
                    var forward3D = new dfloat3(movementVector.x, dfloat.Zero, movementVector.y);
                    forward3D = dmath.normalize(forward3D);
                    transform.rotation = dquaternion.LookRotationSafe(forward3D, dmath.up());
                }
            }
            else
            {
                // Try to slide along obstacles by testing partial movement
                var partialMovement = movementVector * new dfloat(0.5f);
                var partialPosition =
                    transform.position + new dfloat3(partialMovement.x, dfloat.Zero, partialMovement.y);

                if (IsPositionWalkable(new float2((float)partialPosition.x, (float)partialPosition.z),
                        follower.movementType))
                {
                    transform.position = partialPosition;

                    // Update rotation to face movement direction if moving (DETERMINISTIC)
                    if (dmath.lengthsq(partialMovement) > dfloat.Zero)
                    {
                        // Convert 2D movement vector to 3D forward direction
                        var forward3D = new dfloat3(partialMovement.x, dfloat.Zero, partialMovement.y);
                        forward3D = dmath.normalize(forward3D);
                        transform.rotation = dquaternion.LookRotationSafe(forward3D, dmath.up());
                    }
                }
                // If even partial movement fails, don't move
            }
        }

        private dfloat2 GetFlowFieldDirection(dfloat2 worldPos, int targetId, MovementType movementType)
        {
            // Find the flow field entity with matching targetId
            for (int i = 0; i < flowFieldEntities.Length; i++)
            {
                var entity = flowFieldEntities[i];
                if (!flowFieldGridLookup.HasComponent(entity)) continue;

                var gridData = flowFieldGridLookup[entity];
                if (gridData.targetId != targetId) continue;

                // Found matching flow field, sample it
                if (flowFieldLookup.HasBuffer(entity))
                {
                    var buffer = flowFieldLookup[entity];
                    return FlowFieldUtils.SampleFlowField(buffer, worldPos, gridData.worldOrigin,
                        gridData.cellSize, gridData.gridSize, movementType);
                }
            }

            // No matching flow field found, return zero direction
            return dfloat2.zero;
        }

        private bool IsPositionWalkable(float2 position, MovementType movementType)
        {
            // Check all flow fields to see if this position is walkable
            for (int i = 0; i < flowFieldEntities.Length; i++)
            {
                var entity = flowFieldEntities[i];
                if (!flowFieldGridLookup.HasComponent(entity)) continue;
                if (!flowFieldLookup.HasBuffer(entity)) continue;

                var gridData = flowFieldGridLookup[entity];
                var buffer = flowFieldLookup[entity];

                var relativePos = new dfloat2((dfloat)position.x, (dfloat)position.y) - gridData.worldOrigin;
                var gridPos = new int2((int)(relativePos.x / gridData.cellSize.x),
                    (int)(relativePos.y / gridData.cellSize.y));

                if (FlowFieldGridUtils.IsValidGridPosition(gridPos, gridData.gridSize))
                {
                    var index = FlowFieldGridUtils.GridToIndex(gridPos, gridData.gridSize);
                    if (index < buffer.Length)
                    {
                        var cell = buffer[index].cell;
                        return cell.IsWalkableFor(movementType);
                    }
                }

                // If position is outside grid, consider it unwalkable
                return false;
            }

            // If no flow field found, assume walkable (fallback)
            return true;
        }

        private dfloat2 PreventTargetOvershoot(dfloat2 currentPos, dfloat2 movement, int targetId,
            dfloat proximityThreshold)
        {
            // Find the target data with matching targetId
            for (int i = 0; i < targetData.Length; i++)
            {
                var target = targetData[i];
                if (!target.isValid || target.targetId != targetId) continue;

                // Calculate current distance to target (DETERMINISTIC)
                var targetPos = new dfloat2(target.targetPosition.x, target.targetPosition.y);
                var toTarget = targetPos - currentPos;
                var distanceToTarget = dmath.Sqrt(toTarget.x * toTarget.x + toTarget.y * toTarget.y);

                // If we're very close to the target, limit movement to prevent overshoot
                var movementMagnitude = dmath.Sqrt(movement.x * movement.x + movement.y * movement.y);

                // Start decelerating when within 3x the proximity threshold
                var decelerationDistance = proximityThreshold * new dfloat(3.0f);

                if (distanceToTarget < decelerationDistance)
                {
                    // Stop completely when extremely close to target to prevent jittering
                    var stopThreshold = proximityThreshold * new dfloat(0.05f);
                    if (distanceToTarget < stopThreshold)
                    {
                        movement = dfloat2.zero;
                    }
                    else
                    {
                        // Calculate how much we can move without overshooting
                        var maxMovementDistance = dmath.max(distanceToTarget - proximityThreshold * new dfloat(0.1f),
                            dfloat.Zero);

                        if (movementMagnitude > maxMovementDistance)
                        {
                            // Scale down movement to prevent overshoot
                            if (movementMagnitude > dfloat.Zero)
                            {
                                var scale = maxMovementDistance / movementMagnitude;
                                movement *= scale;
                            }
                            else
                            {
                                movement = dfloat2.zero;
                            }
                        }
                    }
                }

                break; // Found target, no need to continue
            }

            return movement;
        }

        private dfloat GetDistanceToTarget(dfloat2 worldPos, int targetId)
        {
            // Find the target data with matching targetId
            for (int i = 0; i < targetData.Length; i++)
            {
                var target = targetData[i];
                if (!target.isValid || target.targetId != targetId) continue;

                // Found matching target, calculate distance (DETERMINISTIC)
                var targetPos = new dfloat2(target.targetPosition.x, target.targetPosition.y);
                var diff = targetPos - worldPos;
                return dmath.Sqrt(diff.x * diff.x + diff.y * diff.y);
            }

            // No target found, return large distance
            return new dfloat(1000.0f);
        }

        private dfloat2 GetDirectTargetDirection(dfloat2 currentPos, int targetId, dfloat proximityThreshold)
        {
            // Find the target data with matching targetId
            for (int i = 0; i < targetData.Length; i++)
            {
                var target = targetData[i];
                if (!target.isValid || target.targetId != targetId) continue;

                // Calculate direction to target (DETERMINISTIC)
                var targetPos = new dfloat2(target.targetPosition.x, target.targetPosition.y);
                var toTarget = targetPos - currentPos;
                var distance = dmath.Sqrt(toTarget.x * toTarget.x + toTarget.y * toTarget.y);

                // Only provide direct direction when very close to target
                var directTargetRange = proximityThreshold * new dfloat(2.0f);
                if (distance < directTargetRange && distance > new dfloat(0.01f))
                {
                    // Return normalized direction to target
                    return toTarget / distance;
                }

                break; // Found target, no need to continue
            }

            return dfloat2.zero;
        }
    }
}